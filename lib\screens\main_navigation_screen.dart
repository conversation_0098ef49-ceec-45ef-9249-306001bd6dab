import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/widget_service.dart';
import '../providers/objectives_provider.dart';
import 'home_screen.dart';
import 'analytics_screen.dart';
import 'settings_screen.dart';
import 'add_objective_screen.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen>
    with TickerProviderStateMixin {
  int _currentIndex = 0;
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _animation;

  final List<Widget> _screens = [
    const HomeScreen(),
    const AnalyticsScreen(),
    const SettingsScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
    
    // Set up widget service callbacks
    _setupWidgetCallbacks();
  }
  
  void _setupWidgetCallbacks() {
    // Handle navigation to add objective screen from widget
    WidgetService.setNavigateToAddObjectiveCallback(() {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const AddObjectiveScreen(),
        ),
      );
    });
    
    // Handle objective completion from widget
    WidgetService.setCompleteObjectiveCallback((int objectiveId) async {
      final provider = Provider.of<ObjectivesProvider>(context, listen: false);
      await provider.markObjectiveComplete(objectiveId);
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onTabTapped(int index) {
    if (_currentIndex != index) {
      setState(() {
        _currentIndex = index;
      });
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOutCubic,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;

    return Scaffold(
      body: isLandscape 
        ? _buildLandscapeLayout()
        : _buildPortraitLayout(),
    );
  }
  
  Widget _buildLandscapeLayout() {
    return Row(
      children: [
        // Navigation rail on the left side
        NavigationRail(
          selectedIndex: _currentIndex,
          onDestinationSelected: _onTabTapped,
          labelType: NavigationRailLabelType.selected,
          useIndicator: true,
          destinations: [
            NavigationRailDestination(
              icon: const Icon(Icons.home),
              selectedIcon: Icon(
                Icons.home,
                color: Theme.of(context).colorScheme.primary,
              ),
              label: const Text('Home'),
            ),
            NavigationRailDestination(
              icon: const Icon(Icons.analytics),
              selectedIcon: Icon(
                Icons.analytics,
                color: Theme.of(context).colorScheme.primary,
              ),
              label: const Text('Analytics'),
            ),
            NavigationRailDestination(
              icon: const Icon(Icons.settings),
              selectedIcon: Icon(
                Icons.settings,
                color: Theme.of(context).colorScheme.primary,
              ),
              label: const Text('Settings'),
            ),
          ],
        ),
        
        // Content area (takes the remaining space)
        Expanded(
          child: PageView(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            children: _screens,
          ),
        ),
      ],
    );
  }
  
  Widget _buildPortraitLayout() {
    return Column(
      children: [
        // Main content area
        Expanded(
          child: PageView(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            children: _screens,
          ),
        ),
        
        // Bottom navigation bar
        AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, 100 * (1 - _animation.value)),
              child: BottomNavigationBar(
                currentIndex: _currentIndex,
                onTap: _onTabTapped,
                type: BottomNavigationBarType.fixed,
                elevation: 8,
                selectedItemColor: Theme.of(context).colorScheme.primary,
                unselectedItemColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                selectedLabelStyle: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                ),
                unselectedLabelStyle: const TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 11,
                ),
                items: [
                  BottomNavigationBarItem(
                    icon: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: EdgeInsets.all(_currentIndex == 0 ? 8 : 4),
                      decoration: BoxDecoration(
                        color: _currentIndex == 0
                            ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.home,
                        size: _currentIndex == 0 ? 26 : 24,
                      ),
                    ),
                    label: 'Home',
                  ),
                  BottomNavigationBarItem(
                    icon: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: EdgeInsets.all(_currentIndex == 1 ? 8 : 4),
                      decoration: BoxDecoration(
                        color: _currentIndex == 1
                            ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.analytics,
                        size: _currentIndex == 1 ? 26 : 24,
                      ),
                    ),
                    label: 'Analytics',
                  ),
                  BottomNavigationBarItem(
                    icon: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: EdgeInsets.all(_currentIndex == 2 ? 8 : 4),
                      decoration: BoxDecoration(
                        color: _currentIndex == 2
                            ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.settings,
                        size: _currentIndex == 2 ? 26 : 24,
                      ),
                    ),
                    label: 'Settings',
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }
}