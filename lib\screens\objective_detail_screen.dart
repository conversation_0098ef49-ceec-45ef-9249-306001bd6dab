import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/objective.dart' as models;
import '../providers/objectives_provider.dart';
import '../widgets/reflection_item.dart';

class ObjectiveDetailScreen extends StatefulWidget {
  final models.Objective objective;

  const ObjectiveDetailScreen({
    super.key,
    required this.objective,
  });

  @override
  State<ObjectiveDetailScreen> createState() => _ObjectiveDetailScreenState();
}

class _ObjectiveDetailScreenState extends State<ObjectiveDetailScreen> {
  final _reflectionController = TextEditingController();
  late models.Objective _objective;
  bool _isEditing = false;
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  bool _hasReminder = false;
  TimeOfDay? _reminderTime;
  int _selectedColor = 0xFF2196F3; // Default blue color

  // Predefined color options
  final List<int> _colorOptions = [
    0xFF2196F3, // Blue
    0xFF4CAF50, // Green
    0xFFFF9800, // Orange
    0xFFF44336, // Red
    0xFF9C27B0, // Purple
    0xFF00BCD4, // Cyan
    0xFFFFEB3B, // Yellow
    0xFF795548, // Brown
    0xFF607D8B, // Blue Grey
    0xFFE91E63, // Pink
  ];

  @override
  void initState() {
    super.initState();
    _objective = widget.objective;
    _initEditingControllers();
  }

  void _initEditingControllers() {
    _titleController.text = _objective.title;
    _descriptionController.text = _objective.description;
    _hasReminder = _objective.hasReminder;
    _selectedColor = _objective.color;
    
    if (_objective.reminderTime != null) {
      _reminderTime = TimeOfDay(
        hour: _objective.reminderTime!.hour,
        minute: _objective.reminderTime!.minute,
      );
    }
  }

  @override
  void dispose() {
    _reflectionController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Objective' : _objective.title),
        actions: [
          if (!_isEditing) ...[
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _confirmDelete,
            ),
          ],
        ],
      ),
      body: _isEditing ? _buildEditForm() : _buildDetailView(),
    );
  }

  Widget _buildDetailView() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildObjectiveHeader(),
          const Divider(height: 32),
          _buildStreakSection(),
          const Divider(height: 32),
          _buildReminderSection(),
          const Divider(height: 32),
          _buildReflectionSection(),
        ],
      ),
    );
  }

  Widget _buildObjectiveHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          _objective.title,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        Text(
          'Started on ${_objective.formattedStartDate}',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
              ),
        ),
        if (_objective.description.isNotEmpty) ...[
          const SizedBox(height: 16),
          Text(
            _objective.description,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ],
    );
  }

  Widget _buildStreakSection() {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Streak Tracking',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStreakCard(
                title: 'Current Streak',
                value: _objective.streakText,
                icon: Icons.local_fire_department,
                color: Colors.orange,
                isActive: _objective.currentStreak > 0,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStreakCard(
                title: 'Longest Streak',
                value: _objective.longestStreakText,
                icon: Icons.emoji_events,
                color: Colors.amber,
                isActive: _objective.longestStreak > 0,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Text(
              'Completed Today:',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(width: 8),
            Checkbox(
              value: _objective.isCompleteForToday(),
              onChanged: (value) {
                if (value == true) {
                  _markComplete();
                }
              },
              activeColor: colorScheme.primary,
            ),
            const Spacer(),
            ElevatedButton.icon(
              onPressed: _objective.isCompleteForToday() 
                  ? null 
                  : _markComplete,
              icon: const Icon(Icons.check),
              label: const Text('Mark Complete'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStreakCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required bool isActive,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: isActive ? color : colorScheme.onSurface.withOpacity(0.3),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isActive ? color : null,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReminderSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Reminder',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 16),
        ListTile(
          leading: Icon(
            _objective.hasReminder
                ? Icons.notifications_active
                : Icons.notifications_off,
            color: _objective.hasReminder
                ? Theme.of(context).colorScheme.primary
                : null,
          ),
          title: Text(
            _objective.hasReminder ? 'Daily Reminder' : 'No Reminder Set',
          ),
          subtitle: _objective.hasReminder && _objective.reminderTime != null
              ? Text('Daily at ${_objective.reminderTime}')
              : null,
        ),
      ],
    );
  }

  Widget _buildReflectionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Reflections',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 16),
        TextField(
          controller: _reflectionController,
          decoration: InputDecoration(
            hintText: 'Add a reflection note...',
            suffixIcon: IconButton(
              icon: const Icon(Icons.send),
              onPressed: _addReflection,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(19),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(19),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(19),
              borderSide: BorderSide.none,
            ),
            filled: true,
          ),
          maxLines: 3,
          textCapitalization: TextCapitalization.sentences,
        ),
        const SizedBox(height: 16),
        _objective.reflections.isEmpty
            ? Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'No reflections yet. Add your thoughts about this objective!',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context)
                              .colorScheme
                              .onBackground
                              .withOpacity(0.7),
                        ),
                    textAlign: TextAlign.center,
                  ),
                ),
              )
            : ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _objective.reflections.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final reflection = _objective.reflections[
                      _objective.reflections.length - 1 - index];
                  return ReflectionItem(
                    reflection: reflection,
                    onEdit: _editReflection,
                    onDelete: _deleteReflection,
                  );
                },
              ),
      ],
    );
  }

  Widget _buildEditForm() {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildTextField(
              controller: _titleController,
              labelText: 'Title',
              hintText: 'What is your objective?',
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a title';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: _descriptionController,
              labelText: 'Description (Optional)',
              hintText: 'Add some details about your objective',
              maxLines: 3,
            ),
            const SizedBox(height: 24),
            _buildColorSelector(),
            const SizedBox(height: 24),
            SwitchListTile(
              title: const Text('Set Daily Reminder'),
              subtitle: Text(
                _hasReminder
                    ? _reminderTime != null
                        ? 'Daily at ${_reminderTime!.format(context)}'
                        : 'Tap to set time'
                    : 'No reminder set',
              ),
              value: _hasReminder,
              onChanged: (value) {
                setState(() {
                  _hasReminder = value;
                  if (value && _reminderTime == null) {
                    _showTimePicker();
                  }
                });
              },
              secondary: Icon(
                _hasReminder
                    ? Icons.notifications_active
                    : Icons.notifications_off,
                color: _hasReminder
                    ? Theme.of(context).colorScheme.primary
                    : null,
              ),
            ),
            if (_hasReminder) ...[
              ListTile(
                title: const Text('Reminder Time'),
                subtitle: Text(
                  _reminderTime != null
                      ? _reminderTime!.format(context)
                      : 'Tap to set time',
                ),
                leading: const Icon(Icons.access_time),
                onTap: _showTimePicker,
              ),
            ],
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () {
                      setState(() {
                        _isEditing = false;
                        _initEditingControllers();
                      });
                    },
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _saveObjective,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text('Save Changes'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(19),
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
      ),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          alignLabelWithHint: true,
          floatingLabelBehavior: FloatingLabelBehavior.always,
          contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          filled: false,
        ),
        maxLines: maxLines,
        validator: validator,
        textCapitalization: TextCapitalization.sentences,
      ),
    );
  }

  Widget _buildColorSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4.0),
          child: Text(
            'Choose Color',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          height: 60,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _colorOptions.length,
            itemBuilder: (context, index) {
              final color = _colorOptions[index];
              final isSelected = color == _selectedColor;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedColor = color;
                  });
                },
                child: Container(
                  margin: const EdgeInsets.only(right: 12),
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Color(color),
                    shape: BoxShape.circle,
                    border: isSelected
                        ? Border.all(
                            color: Theme.of(context).colorScheme.primary,
                            width: 3,
                          )
                        : null,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: isSelected
                      ? Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 24,
                        )
                      : null,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  void _showTimePicker() async {
    final pickedTime = await showTimePicker(
      context: context,
      initialTime: _reminderTime ?? TimeOfDay.now(),
    );

    if (pickedTime != null) {
      setState(() {
        _reminderTime = pickedTime;
      });
    }
  }

  void _saveObjective() async {
    if (_formKey.currentState!.validate()) {
      // Update the objective
      final updatedObjective = models.Objective(
        id: _objective.id,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        startDate: _objective.startDate,
        hasReminder: _hasReminder,
        reminderTime: _hasReminder && _reminderTime != null
            ? models.CustomTimeOfDay( // Use CustomTimeOfDay
                hour: _reminderTime!.hour,
                minute: _reminderTime!.minute,
              )
            : null,
        currentStreak: _objective.currentStreak,
        longestStreak: _objective.longestStreak,
        color: _selectedColor,
        completedDays: _objective.completedDays,
        reflections: _objective.reflections,
      );

      // Update in provider
      await Provider.of<ObjectivesProvider>(context, listen: false)
          .updateObjective(updatedObjective);

      setState(() {
        _isEditing = false;
        _objective = updatedObjective;
      });
    }
  }

  void _markComplete() async {
    if (_objective.id != null && !_objective.isCompleteForToday()) {
      await Provider.of<ObjectivesProvider>(context, listen: false)
          .markObjectiveComplete(_objective.id!);
      
      // Refresh objective
      final updatedObjective = await Provider.of<ObjectivesProvider>(
        context, 
        listen: false,
      ).objectives.firstWhere((o) => o.id == _objective.id);
      
      setState(() {
        _objective = updatedObjective;
      });
    }
  }

  void _addReflection() async {
    final note = _reflectionController.text.trim();
    if (note.isEmpty || _objective.id == null) return;

    await Provider.of<ObjectivesProvider>(context, listen: false)
        .addReflection(_objective.id!, note);

    _reflectionController.clear();

    // Refresh objective
    final updatedObjective = await Provider.of<ObjectivesProvider>(
      context,
      listen: false,
    ).objectives.firstWhere((o) => o.id == _objective.id);
    
    setState(() {
      _objective = updatedObjective;
    });
  }

  void _editReflection(models.Reflection updatedReflection) async {
    if (_objective.id == null) return;

    await Provider.of<ObjectivesProvider>(context, listen: false)
        .updateReflection(_objective.id!, updatedReflection);

    // Refresh objective
    final updatedObjective = await Provider.of<ObjectivesProvider>(
      context,
      listen: false,
    ).objectives.firstWhere((o) => o.id == _objective.id);
    
    setState(() {
      _objective = updatedObjective;
    });
  }

  void _deleteReflection(models.Reflection reflection) async {
    if (_objective.id == null || reflection.id == null) return;

    await Provider.of<ObjectivesProvider>(context, listen: false)
        .deleteReflection(_objective.id!, reflection.id!);

    // Refresh objective
    final updatedObjective = await Provider.of<ObjectivesProvider>(
      context,
      listen: false,
    ).objectives.firstWhere((o) => o.id == _objective.id);
    
    setState(() {
      _objective = updatedObjective;
    });
  }

  void _confirmDelete() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Objective'),
        content: const Text(
          'Are you sure you want to delete this objective? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context); // Close dialog
              
              if (_objective.id != null) {
                await Provider.of<ObjectivesProvider>(context, listen: false)
                    .deleteObjective(_objective.id!);
                
                Navigator.pop(context, true); // Return to home
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}