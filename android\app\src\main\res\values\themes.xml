<resources>

    <style name="Theme.Android.AppWidgetContainerParent" parent="@android:style/Theme.DeviceDefault">
        <!-- Radius of the outer bound of widgets to make the rounded corners -->
        <item name="appWidgetRadius">16dp</item>
        <!--
        Radius of the inner view's bound of widgets to make the rounded corners.
        It needs to be 8dp or less than the value of appWidgetRadius
        -->
        <item name="appWidgetInnerRadius">8dp</item>
    </style>

    <style name="Theme.Android.AppWidgetContainer" parent="Theme.Android.AppWidgetContainerParent">
        <!-- Apply padding to avoid the content of the widget colliding with the rounded corners -->
        <item name="appWidgetPadding">16dp</item>
    </style>
</resources>