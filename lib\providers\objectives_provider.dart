import 'package:flutter/material.dart';
import '../models/objective.dart';
import '../services/database_service.dart';
import '../services/notification_service.dart';
import '../services/widget_service.dart';

class ObjectivesProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final NotificationService _notificationService = NotificationService();
  
  // Enable notifications since we're using a stub implementation
  final bool _notificationsEnabled = true;
  
  List<Objective> _objectives = [];
  bool _isLoading = false;
  
  List<Objective> get objectives => _objectives;
  bool get isLoading => _isLoading;
  
  Future<void> loadObjectives() async {
    _isLoading = true;
    notifyListeners();
    
    _objectives = await _databaseService.getObjectives();
    
    // Update widget with latest data
    await WidgetService.updateQuickAddWidget(objectives: _objectives);
    
    _isLoading = false;
    notifyListeners();
  }
  
  Future<void> addObjective(Objective objective) async {
    _isLoading = true;
    notifyListeners();
    
    // Set display order to be at the end
    objective.displayOrder = _objectives.length;
    
    final id = await _databaseService.insertObjective(objective);
    objective.id = id;
    
    if (_notificationsEnabled && objective.hasReminder && objective.reminderTime != null) {
      await _notificationService.scheduleObjectiveReminder(objective);
    }
    
    _objectives.add(objective);
    
    // Update widget with latest data
    await WidgetService.updateQuickAddWidget(objectives: _objectives);
    
    _isLoading = false;
    notifyListeners();
  }
  
  Future<void> updateObjective(Objective objective) async {
    _isLoading = true;
    notifyListeners();
    
    await _databaseService.updateObjective(objective);
    
    if (_notificationsEnabled) {
      if (objective.hasReminder && objective.reminderTime != null) {
        await _notificationService.scheduleObjectiveReminder(objective);
      } else if (!objective.hasReminder && objective.id != null) {
        await _notificationService.cancelNotification(objective.id!);
      }
    }
    
    final index = _objectives.indexWhere((obj) => obj.id == objective.id);
    if (index != -1) {
      _objectives[index] = objective;
    }
    
    _isLoading = false;
    notifyListeners();
  }
  
  Future<void> deleteObjective(int id) async {
    _isLoading = true;
    notifyListeners();
    
    await _databaseService.deleteObjective(id);
    
    if (_notificationsEnabled) {
      await _notificationService.cancelNotification(id);
    }
    
    _objectives.removeWhere((objective) => objective.id == id);
    
    // Update widget with latest data
    await WidgetService.updateQuickAddWidget(objectives: _objectives);
    
    _isLoading = false;
    notifyListeners();
  }
  
  Future<void> markObjectiveComplete(int id) async {
    final objective = _objectives.firstWhere((obj) => obj.id == id);
    
    // Toggle completion state
    if (objective.isCompleteForToday()) {
      // Unmark as complete
      objective.unmarkAsComplete();
    } else {
      // Mark as complete
      objective.markAsComplete();
      
      // Check for streak milestones (5, 10, 30, 50, 100 days)
      if (_notificationsEnabled) {
        final milestones = [5, 10, 30, 50, 100];
        if (milestones.contains(objective.currentStreak)) {
          await _notificationService.sendStreakMilestoneNotification(
            objective,
            objective.currentStreak,
          );
        }
      }
    }
    
    await _databaseService.updateObjective(objective);
    
    // Update widget with latest data
    await WidgetService.updateQuickAddWidget(objectives: _objectives);
    
    notifyListeners();
  }
  
  Future<void> addReflection(int objectiveId, String note) async {
    if (note.trim().isEmpty) return;
    
    await _databaseService.addReflection(objectiveId, note);
    
    final index = _objectives.indexWhere((obj) => obj.id == objectiveId);
    if (index != -1) {
      _objectives[index].addReflection(note);
      notifyListeners();
    }
  }

  Future<void> updateReflection(int objectiveId, Reflection updatedReflection) async {
    await _databaseService.updateReflection(updatedReflection);
    
    final index = _objectives.indexWhere((obj) => obj.id == objectiveId);
    if (index != -1) {
      final reflectionIndex = _objectives[index].reflections
          .indexWhere((r) => r.id == updatedReflection.id);
      if (reflectionIndex != -1) {
        _objectives[index].reflections[reflectionIndex] = updatedReflection;
        notifyListeners();
      }
    }
  }

  Future<void> deleteReflection(int objectiveId, int reflectionId) async {
    await _databaseService.deleteReflection(reflectionId);
    
    final index = _objectives.indexWhere((obj) => obj.id == objectiveId);
    if (index != -1) {
      _objectives[index].reflections.removeWhere((r) => r.id == reflectionId);
      notifyListeners();
    }
  }
  
  // Check for broken streaks
  Future<void> checkStreaks() async {
    final today = DateTime.now();
    final yesterday = today.subtract(const Duration(days: 1));
    
    for (final objective in _objectives) {
      if (objective.currentStreak > 0) {
        final completedYesterday = objective.completedDays.any((date) =>
            date.year == yesterday.year &&
            date.month == yesterday.month &&
            date.day == yesterday.day);
        
        if (!completedYesterday && !objective.isCompleteForToday()) {
          // Streak is broken
          if (_notificationsEnabled && objective.currentStreak >= 3) {
            // Only notify for streaks of 3 or more days
            await _notificationService.sendBrokenStreakNotification(objective);
          }
          
          objective.currentStreak = 0;
          await _databaseService.updateObjective(objective);
        }
      }
    }
    
    notifyListeners();
  }

  // Reorder objectives
  Future<void> reorderObjectives(int oldIndex, int newIndex) async {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    
    final objective = _objectives.removeAt(oldIndex);
    _objectives.insert(newIndex, objective);
    
    // Update display orders in database
    await _databaseService.updateObjectiveDisplayOrders(_objectives);
    
    notifyListeners();
  }
}