package com.example.day_1

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.example.day_1/widget"
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        handleIntent(intent)
    }
    
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }
    
    private fun handleIntent(intent: Intent?) {
        intent?.data?.let { uri ->
            when {
             uri.toString().startsWith("dayOne://add_objective") -> {
                 // Navigate to add objective screen
                 flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                     MethodChannel(messenger, CHANNEL).invokeMethod("navigateToAddObjective", null)
                 }
             }
             uri.toString().startsWith("dayOne://complete_objective") -> {
                 // Handle objective completion
                 val objectiveId = uri.getQueryParameter("id")?.toIntOrNull()
                 objectiveId?.let { id ->
                     flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                         MethodChannel(messenger, CHANNEL).invokeMethod("completeObjective", id)
                     }
                 }
             }
             else -> {
                 // Handle other URIs or do nothing
             }
         }
        }
    }
}
