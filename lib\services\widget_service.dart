import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:home_widget/home_widget.dart';
import '../models/objective.dart';
import '../providers/objectives_provider.dart';

class WidgetService {
  static const String _appGroupId = 'group.com.example.day_1';
  static const String _androidWidgetName = 'QuickAddWidgetProvider';
  static const MethodChannel _channel = MethodChannel('com.example.day_1/widget');
  static Function()? _onNavigateToAddObjective;
  static Function(int)? _onCompleteObjective;
  
  /// Initialize the widget service
  static Future<void> initialize() async {
    await HomeWidget.setAppGroupId(_appGroupId);
    _setupMethodChannel();
  }
  
  /// Setup method channel for widget interactions
  static void _setupMethodChannel() {
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'navigateToAddObjective':
          _onNavigateToAddObjective?.call();
          break;
        case 'completeObjective':
          final int objectiveId = call.arguments;
          _onCompleteObjective?.call(objectiveId);
          break;
      }
    });
  }
  
  /// Set callback for navigation to add objective screen
  static void setNavigateToAddObjectiveCallback(Function() callback) {
    _onNavigateToAddObjective = callback;
  }
  
  /// Set callback for completing an objective
  static void setCompleteObjectiveCallback(Function(int) callback) {
    _onCompleteObjective = callback;
  }
  
  /// Update the Quick Add Widget (1x1 version only needs minimal data)
  static Future<void> updateQuickAddWidget({
    required List<Objective> objectives,
  }) async {
    try {
      // Update the widget - no data needed for 1x1 widget
      await HomeWidget.updateWidget(
        name: _androidWidgetName,
        androidName: _androidWidgetName,
      );
      
    } catch (e) {
      debugPrint('Error updating Quick Add Widget: $e');
    }
  }
  
  /// Handle widget interactions
  static Future<void> handleWidgetInteraction() async {
    try {
      HomeWidget.widgetClicked.listen((Uri? uri) {
        if (uri != null) {
          _handleWidgetClick(uri);
        }
      });
    } catch (e) {
      debugPrint('Error setting up widget interaction: $e');
    }
  }
  
  static void _handleWidgetClick(Uri uri) {
    debugPrint('Widget clicked with URI: $uri');
    
    // Handle different widget actions
    if (uri.host == 'add_objective') {
      // Navigate to add objective screen
      // This will be handled by the main app
    } else if (uri.host == 'complete_objective') {
      final objectiveId = int.tryParse(uri.queryParameters['id'] ?? '');
      if (objectiveId != null) {
        // Mark objective as complete
        // This will be handled by the main app
      }
    }
  }
  
  /// Get widget click stream
  static Stream<Uri?> get widgetClicked => HomeWidget.widgetClicked;
}