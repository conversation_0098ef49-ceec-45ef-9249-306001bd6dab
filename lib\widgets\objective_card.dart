import 'package:flutter/material.dart';
import '../models/objective.dart';
import 'package:intl/intl.dart';

class ObjectiveCard extends StatelessWidget {
  final Objective objective;
  final VoidCallback onTap;
  final Function(bool) onCompleteToggle;
  final bool isDragging;

  const ObjectiveCard({
    super.key,
    required this.objective,
    required this.onTap,
    required this.onCompleteToggle,
    this.isDragging = false,
  });

  @override
  Widget build(BuildContext context) {
    final isCompletedToday = objective.isCompleteForToday();
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      elevation: isDragging ? 8.0 : 2.0,
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            border: Border(
              left: BorderSide(
                color: Color(objective.color),
                width: 4,
              ),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with title and completion
                Row(
                  children: [
                    // Drag handle
                    Container(
                      padding: const EdgeInsets.all(4),
                      child: Icon(
                        Icons.drag_handle,
                        color: colorScheme.onSurface.withOpacity(0.6),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        objective.title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ),
                    _buildCompletionCheckbox(context, isCompletedToday),
                  ],
                ),
                
                if (objective.description.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    objective.description,
                    style: Theme.of(context).textTheme.bodyMedium,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                
                const SizedBox(height: 12),
                _buildDateInfo(context),
                
                // Enhanced Progress Section
                if (objective.endDate != null) ...[
                  const SizedBox(height: 16),
                  _buildProgressSection(context),
                ],
                
                const SizedBox(height: 16),
                
                // Enhanced Streak Section
                _buildStreakSection(context),
                
                if (objective.hasReminder && objective.reminderTime != null) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Icon(
                        Icons.notifications_active,
                        size: 16,
                        color: colorScheme.primary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Daily reminder at ${objective.reminderTime}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: colorScheme.primary,
                            ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCompletionCheckbox(BuildContext context, bool isCompletedToday) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Checkbox(
      value: isCompletedToday,
      onChanged: (value) {
        if (value != null) {
          onCompleteToggle(value);
        }
      },
      activeColor: colorScheme.primary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
  
  Widget _buildDateInfo(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    return Row(
      children: [
        Icon(
          Icons.calendar_today,
          size: 16,
          color: colorScheme.onSurface.withOpacity(0.6),
        ),
        const SizedBox(width: 4),
        Text(
          'Started: ${DateFormat('MMM dd, yyyy').format(objective.startDate)}',
          style: textTheme.bodySmall?.copyWith(
            color: colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        if (objective.endDate != null) ...[
          const SizedBox(width: 16),
          Icon(
            Icons.event,
            size: 16,
            color: colorScheme.onSurface.withOpacity(0.6),
          ),
          const SizedBox(width: 4),
          Text(
            'Ends: ${DateFormat('MMM dd, yyyy').format(objective.endDate!)}',
            style: textTheme.bodySmall?.copyWith(
              color: _isObjectiveExpired() ? Colors.red : colorScheme.onSurface.withOpacity(0.7),
              fontWeight: _isObjectiveExpired() ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ],
    );
  }
  
  bool _isObjectiveExpired() {
    if (objective.endDate == null) return false;
    return DateTime.now().isAfter(objective.endDate!);
  }

  Widget _buildProgressSection(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final percentage = objective.completionPercentage ?? 0.0;
    
    Color progressColor = percentage >= 80 ? Colors.green :
                         percentage >= 50 ? Colors.orange :
                         colorScheme.primary;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(
                  Icons.trending_up,
                  size: 18,
                  color: progressColor,
                ),
                const SizedBox(width: 6),
                Text(
                  'Progress',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: progressColor,
                  ),
                ),
              ],
            ),
            Text(
              '${percentage.toStringAsFixed(0)}%',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: progressColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          height: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: colorScheme.surfaceContainerHighest,
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: LinearProgressIndicator(
              value: percentage / 100,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(progressColor),
              minHeight: 8,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStreakSection(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: objective.currentStreak > 0 
                        ? Colors.orange.withOpacity(0.2) 
                        : colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.local_fire_department,
                    color: objective.currentStreak > 0 
                        ? Colors.orange 
                        : colorScheme.onSurface.withOpacity(0.4),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      objective.currentStreak.toString(),
                      style: textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: objective.currentStreak > 0 
                            ? Colors.orange 
                            : colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                    Text(
                      'Current',
                      style: textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Container(
            width: 1,
            height: 40,
            color: colorScheme.outline.withOpacity(0.3),
          ),
          Expanded(
            child: Row(
              children: [
                const SizedBox(width: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: objective.longestStreak > 0 
                        ? Colors.amber.withOpacity(0.2) 
                        : colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.emoji_events,
                    color: objective.longestStreak > 0 
                        ? Colors.amber 
                        : colorScheme.onSurface.withOpacity(0.4),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      objective.longestStreak.toString(),
                      style: textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: objective.longestStreak > 0 
                            ? Colors.amber 
                            : colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                    Text(
                      'Best',
                      style: textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}