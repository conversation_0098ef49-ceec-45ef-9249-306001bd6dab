import 'package:flutter/material.dart'; // Add this import
import 'package:awesome_notifications/awesome_notifications.dart';
import '../models/objective.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();

  factory NotificationService() => _instance;

  NotificationService._internal();

  Future<void> initialize() async {
    await AwesomeNotifications().initialize(
      // set the icon to null if you want to use the default app icon
      null, // 'resource://drawable/res_app_icon',
      [
        NotificationChannel(
          channelKey: 'basic_channel',
          channelName: 'Basic notifications',
          channelDescription: 'Notification channel for basic tests',
          defaultColor: const Color(0xFF9D50DD),
          ledColor: Colors.white,
          importance: NotificationImportance.High,
          channelShowBadge: true,
        ),
        NotificationChannel(
          channelKey: 'scheduled_channel',
          channelName: 'Scheduled notifications',
          channelDescription: 'Notification channel for scheduled reminders',
          defaultColor: const Color(0xFF9D50DD),
          ledColor: Colors.white,
          importance: NotificationImportance.High,
          channelShowBadge: true,
        )
      ],
      // Channel groups are only visual and are not required
      channelGroups: [
        NotificationChannelGroup(
            channelGroupKey: 'basic_channel_group',
            channelGroupName: 'Basic group')
      ],
      debug: true,
    );
  }

  Future<void> requestPermissions() async {
    bool isAllowed = await AwesomeNotifications().isNotificationAllowed();
    if (!isAllowed) {
      await AwesomeNotifications().requestPermissionToSendNotifications();
    }
  }

  Future<void> scheduleObjectiveReminder(Objective objective) async {
    if (objective.id == null || objective.reminderTime == null) return;

    await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: objective.id!,
        channelKey: 'scheduled_channel',
        title: 'Reminder: ${objective.title}',
        body: 'Don\'t forget to complete your objective: ${objective.description}',
        notificationLayout: NotificationLayout.Default,
      ),
      schedule: NotificationCalendar(
        hour: objective.reminderTime!.hour,
        minute: objective.reminderTime!.minute,
        second: 0,
        millisecond: 0,
        repeats: true,
      ),
    );
    print('Scheduled reminder for: ${objective.title} with awesome_notifications');
  }

  Future<void> cancelNotification(int id) async {
    await AwesomeNotifications().cancel(id);
    print('Canceled notification: $id with awesome_notifications');
  }

  Future<void> sendStreakMilestoneNotification(
      Objective objective, int milestone) async {
    if (objective.id == null) return;
    // Use a different ID for milestone notifications to avoid conflict with reminders
    final milestoneNotificationId = objective.id! + 1000; 

    await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: milestoneNotificationId, 
        channelKey: 'basic_channel',
        title: '🎉 Streak Milestone! 🎉',
        body:
            'Congratulations on reaching a $milestone day streak for "${objective.title}"!',
        notificationLayout: NotificationLayout.Default,
      ),
    );
    print(
        'Streak milestone for: ${objective.title} - $milestone days with awesome_notifications');
  }

  Future<void> sendBrokenStreakNotification(Objective objective) async {
    if (objective.id == null) return;
    // Use a different ID for broken streak notifications
    final brokenStreakNotificationId = objective.id! + 2000;

    await AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: brokenStreakNotificationId, 
        channelKey: 'basic_channel',
        title: ' Streak Broken 😟',
        body: 'Your streak for "${objective.title}" has been broken. Don\'t give up!',
        notificationLayout: NotificationLayout.Default,
      ),
    );
    print('Streak broken for: ${objective.title} with awesome_notifications');
  }
}