import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/objective.dart';

class ProgressCharts {
  // Generate objectives completion line chart data over time
  static List<FlSpot> generateObjectivesCompletionData(List<Objective> objectives, {int days = 30}) {
    final spots = <FlSpot>[];
    final now = DateTime.now();
    
    for (int i = 0; i < days; i++) {
      final date = now.subtract(Duration(days: days - 1 - i));
      final dateOnly = DateTime(date.year, date.month, date.day);
      
      int completedCount = 0;
      int totalObjectives = 0;
      
      for (final objective in objectives) {
        // Only count objectives that were active on this date
        if (dateOnly.isAfter(objective.startDate.subtract(const Duration(days: 1))) &&
            (objective.endDate == null || dateOnly.isBefore(objective.endDate!.add(const Duration(days: 1))))) {
          totalObjectives++;
          
          // Check if this objective was completed on this date
          if (objective.completedDays.any((completedDay) => 
              DateTime(completedDay.year, completedDay.month, completedDay.day) == dateOnly)) {
            completedCount++;
          }
        }
      }
      
      final completionRate = totalObjectives > 0 ? (completedCount / totalObjectives) * 100 : 0;
      spots.add(FlSpot(i.toDouble(), completionRate.toDouble()));
    }
    
    return spots;
  }

  // Generate streak line chart data
  static List<FlSpot> generateStreakData(Objective objective) {
    final spots = <FlSpot>[];
    final completedDays = objective.completedDays;
    
    if (completedDays.isEmpty) return spots;
    
    // Sort completed days
    completedDays.sort();
    
    // Calculate streak progression over time
    int currentStreak = 0;
    DateTime? lastDate;
    
    for (int i = 0; i < completedDays.length; i++) {
      final date = completedDays[i];
      
      if (lastDate == null || date.difference(lastDate).inDays == 1) {
        currentStreak++;
      } else {
        currentStreak = 1;
      }
      
      spots.add(FlSpot(i.toDouble(), currentStreak.toDouble()));
      lastDate = date;
    }
    
    return spots;
  }
  
  // Generate weekly completion data
  static List<BarChartGroupData> generateWeeklyData(List<Objective> objectives) {
    final weeklyData = List.filled(7, 0);
    final now = DateTime.now();
    
    for (final objective in objectives) {
      for (final completedDay in objective.completedDays) {
        final daysDiff = now.difference(completedDay).inDays;
        if (daysDiff >= 0 && daysDiff < 7) {
          final weekday = (7 - daysDiff - 1) % 7;
          weeklyData[weekday]++;
        }
      }
    }
    
    return weeklyData.asMap().entries.map((entry) {
      return BarChartGroupData(
        x: entry.key,
        barRods: [
          BarChartRodData(
            toY: entry.value.toDouble(),
            color: Colors.blue,
            width: 20,
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      );
    }).toList();
  }




}

class StreakLineChart extends StatelessWidget {
  final Objective objective;
  
  const StreakLineChart({super.key, required this.objective});
  
  @override
  Widget build(BuildContext context) {
    final spots = ProgressCharts.generateStreakData(objective);
    
    if (spots.isEmpty) {
      return const Center(
        child: Text(
          'No data available yet',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }
    
    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: true,
            horizontalInterval: 1,
            verticalInterval: 1,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Colors.grey.withValues(alpha: 0.3),
                strokeWidth: 1,
              );
            },
            getDrawingVerticalLine: (value) {
              return FlLine(
                color: Colors.grey.withValues(alpha: 0.3),
                strokeWidth: 1,
              );
            },
          ),
          titlesData: FlTitlesData(
            show: true,
            rightTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                interval: 1,
                getTitlesWidget: (value, meta) {
                  return SideTitleWidget(
                    axisSide: meta.axisSide,
                    child: Text(
                      value.toInt().toString(),
                      style: const TextStyle(fontSize: 12),
                    ),
                  );
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                interval: 1,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toInt().toString(),
                    style: const TextStyle(fontSize: 12),
                  );
                },
                reservedSize: 42,
              ),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: const Color(0xff37434d)),
          ),
          minX: 0,
          maxX: spots.length.toDouble() - 1,
          minY: 0,
          maxY: spots.map((spot) => spot.y).reduce((a, b) => a > b ? a : b) + 1,
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true,
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.secondary,
                ],
              ),
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) {
                  return FlDotCirclePainter(
                    radius: 4,
                    color: Theme.of(context).colorScheme.primary,
                    strokeWidth: 2,
                    strokeColor: Colors.white,
                  );
                },
              ),
              belowBarData: BarAreaData(
                show: true,
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                    Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class WeeklyCompletionChart extends StatelessWidget {
  final List<Objective> objectives;
  
  const WeeklyCompletionChart({super.key, required this.objectives});
  
  @override
  Widget build(BuildContext context) {
    final barGroups = ProgressCharts.generateWeeklyData(objectives);
    final weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    
    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: barGroups.isEmpty ? 10 : barGroups
              .map((group) => group.barRods.first.toY)
              .reduce((a, b) => a > b ? a : b) + 2,
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              getTooltipColor: (group) => Colors.blueGrey,
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                return BarTooltipItem(
                  '${weekdays[group.x.toInt()]}\n',
                  const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  children: <TextSpan>[
                    TextSpan(
                      text: '${rod.toY.round()} completions',
                      style: const TextStyle(
                        color: Colors.yellow,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            rightTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (double value, TitleMeta meta) {
                  return SideTitleWidget(
                    axisSide: meta.axisSide,
                    child: Text(
                      weekdays[value.toInt()],
                      style: const TextStyle(fontSize: 12),
                    ),
                  );
                },
                reservedSize: 38,
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 28,
                interval: 1,
                getTitlesWidget: (double value, TitleMeta meta) {
                  return Text(
                    value.toInt().toString(),
                    style: const TextStyle(fontSize: 12),
                  );
                },
              ),
            ),
          ),
          borderData: FlBorderData(
            show: false,
          ),
          barGroups: barGroups,
          gridData: FlGridData(show: false),
        ),
      ),
    );
  }
}



class ObjectivesLineChart extends StatelessWidget {
  final List<Objective> objectives;
  final int days;
  
  const ObjectivesLineChart({
    super.key, 
    required this.objectives,
    this.days = 30,
  });
  
  @override
  Widget build(BuildContext context) {
    final spots = ProgressCharts.generateObjectivesCompletionData(objectives, days: days);
    
    if (spots.isEmpty || objectives.isEmpty) {
      return const Center(
        child: Text(
          'No completion data available yet',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }
    
    return Container(
      height: 250,
      padding: const EdgeInsets.all(16),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: true,
            horizontalInterval: 25,
            verticalInterval: days > 30 ? 10 : 5,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Colors.grey.withValues(alpha: 0.3),
                strokeWidth: 1,
              );
            },
            getDrawingVerticalLine: (value) {
              return FlLine(
                color: Colors.grey.withValues(alpha: 0.3),
                strokeWidth: 1,
              );
            },
          ),
          titlesData: FlTitlesData(
            show: true,
            rightTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                interval: days > 30 ? 10 : 5,
                getTitlesWidget: (value, meta) {
                  final dayIndex = value.toInt();
                  if (dayIndex < 0 || dayIndex >= days) return const Text('');
                  
                  final date = DateTime.now().subtract(Duration(days: days - 1 - dayIndex));
                  return SideTitleWidget(
                    axisSide: meta.axisSide,
                    child: Text(
                      '${date.month}/${date.day}',
                      style: const TextStyle(fontSize: 10),
                    ),
                  );
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                interval: 25,
                getTitlesWidget: (value, meta) {
                  return Text(
                    '${value.toInt()}%',
                    style: const TextStyle(fontSize: 12),
                  );
                },
                reservedSize: 42,
              ),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: Theme.of(context).dividerColor),
          ),
          minX: 0,
          maxX: (days - 1).toDouble(),
          minY: 0,
          maxY: 100,
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true,
              gradient: LinearGradient(
                colors: [
                  Colors.blue.withValues(alpha: 0.8),
                  Colors.blue.withValues(alpha: 0.3),
                ],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) {
                  return FlDotCirclePainter(
                    radius: 4,
                    color: Colors.blue,
                    strokeWidth: 2,
                    strokeColor: Colors.white,
                  );
                },
              ),
              belowBarData: BarAreaData(
                show: true,
                gradient: LinearGradient(
                  colors: [
                    Colors.blue.withValues(alpha: 0.3),
                    Colors.blue.withValues(alpha: 0.1),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
          ],
          lineTouchData: LineTouchData(
            enabled: true,
            touchTooltipData: LineTouchTooltipData(
              getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                return touchedBarSpots.map((barSpot) {
                  final dayIndex = barSpot.x.toInt();
                  final date = DateTime.now().subtract(Duration(days: days - 1 - dayIndex));
                  return LineTooltipItem(
                    '${date.month}/${date.day}\n${barSpot.y.toStringAsFixed(1)}%',
                    const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                }).toList();
              },
            ),
          ),
        ),
      ),
    );
  }
}