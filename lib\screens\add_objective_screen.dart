import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../models/objective.dart';
import '../providers/objectives_provider.dart';

class AddObjectiveScreen extends StatefulWidget {
  const AddObjectiveScreen({super.key});

  @override
  State<AddObjectiveScreen> createState() => _AddObjectiveScreenState();
}

class _AddObjectiveScreenState extends State<AddObjectiveScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController(); // Define and initialize
  final _descriptionController = TextEditingController(); // Define and initialize
  DateTime _startDate = DateTime.now();
  DateTime? _endDate;
  bool _hasReminder = false;
  TimeOfDay? _selectedTime; // Use material.dart TimeOfDay for picker
  int _selectedColor = 0xFF2196F3; // Default blue color

  // Predefined color options
  final List<int> _colorOptions = [
    0xFF2196F3, // Blue
    0xFF4CAF50, // Green
    0xFFFF9800, // Orange
    0xFFF44336, // Red
    0xFF9C27B0, // Purple
    0xFF00BCD4, // Cyan
    0xFFFFEB3B, // Yellow
    0xFF795548, // Brown
    0xFF607D8B, // Blue Grey
    0xFFE91E63, // Pink
  ];

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add New Objective'),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildTextField(
                controller: _titleController,
                labelText: 'Title',
                hintText: 'What is your objective?',
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              _buildTextField(
                controller: _descriptionController,
                labelText: 'Description (Optional)',
                hintText: 'Add some details about your objective',
                maxLines: 3,
              ),
              const SizedBox(height: 24),
              _buildColorSelector(),
              const SizedBox(height: 24),
              ListTile(
                title: const Text('Start Date'),
                subtitle: Text(DateFormat('MMM dd, yyyy').format(_startDate)),
                leading: const Icon(Icons.calendar_today),
                onTap: () => _selectStartDate(context),
              ),
              const SizedBox(height: 16),
              ListTile(
                title: const Text('End Date (Optional)'),
                subtitle: Text(
                  _endDate != null
                      ? DateFormat('MMM dd, yyyy').format(_endDate!)
                      : 'Tap to set end date',
                ),
                leading: const Icon(Icons.event),
                trailing: _endDate != null
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _endDate = null;
                          });
                        },
                      )
                    : null,
                onTap: () => _selectEndDate(context),
              ),
              const SizedBox(height: 24),
              CheckboxListTile(
                title: const Text('Set Reminder'),
                value: _hasReminder,
                onChanged: (bool? value) {
                  setState(() {
                    _hasReminder = value ?? false;
                    if (!_hasReminder) {
                      _selectedTime = null;
                    }
                  });
                },
              ),
              if (_hasReminder) ...[
                ListTile(
                  title: const Text('Reminder Time'),
                  subtitle: Text(
                    _selectedTime != null // Use _selectedTime
                        ? _selectedTime!.format(context)
                        : 'Tap to set time',
                  ),
                  leading: const Icon(Icons.access_time),
                  onTap: () => _selectTime(context), // Call _selectTime
                ),
              ],
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _saveObjective,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Save Objective'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(19),
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
      ),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          alignLabelWithHint: true,
          floatingLabelBehavior: FloatingLabelBehavior.always,
          contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          filled: false,
        ),
        maxLines: maxLines,
        validator: validator,
        textCapitalization: TextCapitalization.sentences,
      ),
    );
  }

  Future<void> _selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );
    if (picked != null && picked != _startDate) {
      setState(() {
        _startDate = picked;
        // If end date is before start date, clear it
        if (_endDate != null && _endDate!.isBefore(_startDate)) {
          _endDate = null;
        }
      });
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate ?? _startDate.add(const Duration(days: 30)),
      firstDate: _startDate,
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );
    if (picked != null) {
      setState(() {
        _endDate = picked;
      });
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime ?? TimeOfDay.now(),
    );
    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
      });
    }
  }

  Widget _buildColorSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4.0),
          child: Text(
            'Choose Color',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(height: 12),
        Container(
          height: 60,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _colorOptions.length,
            itemBuilder: (context, index) {
              final color = _colorOptions[index];
              final isSelected = color == _selectedColor;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedColor = color;
                  });
                },
                child: Container(
                  margin: const EdgeInsets.only(right: 12),
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Color(color),
                    shape: BoxShape.circle,
                    border: isSelected
                        ? Border.all(
                            color: Theme.of(context).colorScheme.primary,
                            width: 3,
                          )
                        : null,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: isSelected
                      ? Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 24,
                        )
                      : null,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  void _saveObjective() {
    if (_formKey.currentState!.validate()) {
      // No need for _formKey.currentState!.save(); with controllers

      CustomTimeOfDay? reminderTimeForObjective;
      if (_hasReminder && _selectedTime != null) {
        reminderTimeForObjective = CustomTimeOfDay(
          hour: _selectedTime!.hour,
          minute: _selectedTime!.minute,
        );
      }

      final newObjective = Objective(
        title: _titleController.text, // Get text from controller
        description: _descriptionController.text, // Get text from controller
        startDate: _startDate,
        endDate: _endDate,
        hasReminder: _hasReminder,
        reminderTime: reminderTimeForObjective, // Use CustomTimeOfDay
        color: _selectedColor,
      );

      Provider.of<ObjectivesProvider>(context, listen: false)
          .addObjective(newObjective);
      Navigator.pop(context, true); // Indicate success
    }
  }
}