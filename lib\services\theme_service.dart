import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeService extends ChangeNotifier {
  static const String _themeKey = 'theme_mode';
  
  late ThemeMode _themeMode;
  
  ThemeMode get themeMode => _themeMode;
  
  bool get isDarkMode => _themeMode == ThemeMode.dark;
  
  ThemeService() {
    // _loadTheme(); // Remove from constructor
  }
  
  Future<void> loadTheme() async { // Make public
    final prefs = await SharedPreferences.getInstance();
    final savedTheme = prefs.getString(_themeKey);
    
    if (savedTheme == 'dark') {
      _themeMode = ThemeMode.dark;
    } else if (savedTheme == 'light') {
      _themeMode = ThemeMode.light;
    } else {
      // Default to system
      _themeMode = ThemeMode.system;
    }
    
    notifyListeners();
  }
  
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;
    
    _themeMode = mode;
    
    final prefs = await SharedPreferences.getInstance();
    String themeString;
    
    switch (mode) {
      case ThemeMode.dark:
        themeString = 'dark';
        break;
      case ThemeMode.light:
        themeString = 'light';
        break;
      default:
        themeString = 'system';
    }
    
    await prefs.setString(_themeKey, themeString);
    notifyListeners();
  }
  
  Future<void> toggleTheme() async {
    if (_themeMode == ThemeMode.dark) {
      await setThemeMode(ThemeMode.light);
    } else {
      await setThemeMode(ThemeMode.dark);
    }
  }
  
  // Theme data
  static ThemeData get lightTheme {
    return ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.light,
      ),
      useMaterial3: true,
      fontFamily: 'Ubuntu',
      textTheme: const TextTheme(
        displayLarge: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w700),
        displayMedium: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w700),
        displaySmall: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w700),
        headlineLarge: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w700),
        headlineMedium: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w700),
        headlineSmall: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w500),
        titleLarge: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w500),
        titleMedium: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w500),
        titleSmall: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w500),
        bodyLarge: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w400),
        bodyMedium: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w400),
        bodySmall: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w400),
        labelLarge: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w500),
        labelMedium: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w500),
        labelSmall: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w400),
      ),
      appBarTheme: AppBarTheme(
        elevation: 0,
        centerTitle: true,
        backgroundColor: ColorScheme.fromSeed(seedColor: Colors.blue).surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        titleTextStyle: const TextStyle(
          fontFamily: 'Ubuntu',
          fontWeight: FontWeight.w500,
          fontSize: 20,
          color: Colors.black,
        ),
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        elevation: 4,
        shape: CircleBorder(),
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        labelStyle: const TextStyle(
          fontFamily: 'Ubuntu',
          fontWeight: FontWeight.w400,
        ),
        hintStyle: const TextStyle(
          fontFamily: 'Ubuntu',
          fontWeight: FontWeight.w400,
        ),
      ),
      dividerTheme: const DividerThemeData(
        space: 20,
        thickness: 1,
      ),
    );
  }
  
  static ThemeData get darkTheme {
    return ThemeData(
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.dark,
      ),
      useMaterial3: true,
      fontFamily: 'Ubuntu',
      textTheme: const TextTheme(
        displayLarge: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w700),
        displayMedium: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w700),
        displaySmall: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w700),
        headlineLarge: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w700),
        headlineMedium: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w700),
        headlineSmall: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w500),
        titleLarge: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w500),
        titleMedium: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w500),
        titleSmall: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w500),
        bodyLarge: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w400),
        bodyMedium: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w400),
        bodySmall: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w400),
        labelLarge: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w500),
        labelMedium: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w500),
        labelSmall: TextStyle(fontFamily: 'Ubuntu', fontWeight: FontWeight.w400),
      ),
      appBarTheme: AppBarTheme(
        elevation: 0,
        centerTitle: true,
        backgroundColor: ColorScheme.fromSeed(seedColor: Colors.blue, brightness: Brightness.dark).surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        titleTextStyle: const TextStyle(
          fontFamily: 'Ubuntu',
          fontWeight: FontWeight.w500,
          fontSize: 20,
        ),
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        elevation: 4,
        shape: CircleBorder(),
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        labelStyle: const TextStyle(
          fontFamily: 'Ubuntu',
          fontWeight: FontWeight.w400,
        ),
        hintStyle: const TextStyle(
          fontFamily: 'Ubuntu',
          fontWeight: FontWeight.w400,
        ),
      ),
      dividerTheme: const DividerThemeData(
        space: 20,
        thickness: 1,
      ),
    );
  }
}