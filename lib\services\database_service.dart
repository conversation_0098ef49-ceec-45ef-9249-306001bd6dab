import 'dart:async';
import 'dart:convert';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import '../models/objective.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  
  factory DatabaseService() => _instance;
  
  DatabaseService._internal();
  
  static Database? _database;
  
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }
  
  Future<Database> _initDatabase() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, 'day_one.db');
    
    return await openDatabase(
      path,
      version: 5,
      onCreate: _createDB,
      onUpgrade: _upgradeDB,
    );
  }
  
  Future<void> _createDB(Database db, int version) async {
    await db.execute('''
        CREATE TABLE objectives(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL,
          description TEXT,
          startDate TEXT NOT NULL,
          endDate TEXT,
          hasReminder INTEGER NOT NULL DEFAULT 0,
          reminderTime TEXT,
          currentStreak INTEGER NOT NULL DEFAULT 0,
          longestStreak INTEGER NOT NULL DEFAULT 0,
          displayOrder INTEGER NOT NULL DEFAULT 0,
          completedDays TEXT,
          reflections TEXT,
          color INTEGER NOT NULL DEFAULT 4280391931
        )
      ''');
  }
  
  Future<void> _upgradeDB(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      await db.execute('ALTER TABLE objectives ADD COLUMN endDate TEXT');
    }
    if (oldVersion < 3) {
      await db.execute('ALTER TABLE objectives ADD COLUMN displayOrder INTEGER NOT NULL DEFAULT 0');
      // Set initial display order based on current order
      final objectives = await db.query('objectives', orderBy: 'id ASC');
      for (int i = 0; i < objectives.length; i++) {
        await db.update(
          'objectives',
          {'displayOrder': i},
          where: 'id = ?',
          whereArgs: [objectives[i]['id']],
        );
      }
    }
    if (oldVersion < 4) {
      await db.execute('ALTER TABLE objectives ADD COLUMN reflections TEXT');
      // Initialize with empty array for existing objectives
      final objectives = await db.query('objectives');
      for (final objective in objectives) {
        await db.update(
          'objectives',
          {'reflections': '[]'},
          where: 'id = ?',
          whereArgs: [objective['id']],
        );
      }
    }
    if (oldVersion < 5) {
      await db.execute('ALTER TABLE objectives ADD COLUMN color INTEGER NOT NULL DEFAULT 4280391931');
    }
  }
  
  // CRUD Operations
  
  // Create
  Future<int> insertObjective(Objective objective) async {
    final db = await database;
    
    final objMap = objective.toMap();
    
    // Handle reflections separately (serialized as JSON)
    objMap['reflections'] = jsonEncode(
      objective.reflections.map((r) => r.toMap()).toList()
    );
    
    final id = await db.insert('objectives', objMap);
    return id;
  }
  
  // Read
  Future<List<Objective>> getObjectives() async {
    final db = await database;
    
    final List<Map<String, dynamic>> maps = await db.query('objectives', orderBy: 'displayOrder ASC');
    
    return List.generate(maps.length, (index) {
      final obj = Objective.fromMap(maps[index]);
      
      // Handle reflections
      if (maps[index]['reflections'] != null) {
        final List<dynamic> reflectionsJson = jsonDecode(maps[index]['reflections'] as String);
        obj.reflections = reflectionsJson
            .map((reflectionMap) => Reflection.fromMap(reflectionMap as Map<String, dynamic>))
            .toList();
      }
      
      return obj;
    });
  }
  
  Future<Objective?> getObjective(int id) async {
    final db = await database;
    
    final maps = await db.query(
      'objectives',
      where: 'id = ?',
      whereArgs: [id],
    );
    
    if (maps.isEmpty) return null;
    
    final obj = Objective.fromMap(maps.first);
    
    // Handle reflections
    if (maps.first['reflections'] != null) {
      final List<dynamic> reflectionsJson = jsonDecode(maps.first['reflections'] as String);
      obj.reflections = reflectionsJson
          .map((reflectionMap) => Reflection.fromMap(reflectionMap as Map<String, dynamic>))
          .toList();
    }
    
    return obj;
  }
  
  // Update
  Future<int> updateObjective(Objective objective) async {
    final db = await database;
    
    final objMap = objective.toMap();
    
    // Handle reflections separately (serialized as JSON)
    objMap['reflections'] = jsonEncode(
      objective.reflections.map((r) => r.toMap()).toList()
    );
    
    return await db.update(
      'objectives',
      objMap,
      where: 'id = ?',
      whereArgs: [objective.id],
    );
  }
  
  // Delete
  Future<int> deleteObjective(int id) async {
    final db = await database;
    
    return await db.delete(
      'objectives',
      where: 'id = ?',
      whereArgs: [id],
    );
  }
  
  // Method to mark an objective as complete for today
  Future<void> markObjectiveComplete(int id) async {
    final objective = await getObjective(id);
    if (objective == null) return;
    
    objective.markAsComplete();
    await updateObjective(objective);
  }
  
  // Method to add a reflection to an objective
  Future<void> addReflection(int objectiveId, String note) async {
    final objective = await getObjective(objectiveId);
    if (objective == null) return;
    
    objective.addReflection(note);
    await updateObjective(objective);
  }

  // Method to update a reflection
  Future<void> updateReflection(Reflection updatedReflection) async {
    final db = await database;
    await db.update(
      'reflections',
      updatedReflection.toMap(),
      where: 'id = ?',
      whereArgs: [updatedReflection.id],
    );
  }

  // Method to update display orders for reordering
  Future<void> updateObjectiveDisplayOrders(List<Objective> objectives) async {
    final db = await database;
    final batch = db.batch();
    
    for (int i = 0; i < objectives.length; i++) {
      objectives[i].displayOrder = i;
      batch.update(
        'objectives',
        {'displayOrder': i},
        where: 'id = ?',
        whereArgs: [objectives[i].id],
      );
    }
    
    await batch.commit();
  }

  // Method to delete a reflection
  Future<void> deleteReflection(int reflectionId) async {
    final db = await database;
    await db.delete(
      'reflections',
      where: 'id = ?',
      whereArgs: [reflectionId],
    );
  }
}